rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // 学生数据访问规则
    match /students/{studentId} {
      allow read, write: if request.auth != null;
    }
    
    // 课时包数据访问规则
    match /coursePackages/{packageId} {
      allow read, write: if request.auth != null;
    }
    
    // 课时记录数据访问规则
    match /classRecords/{recordId} {
      allow read, write: if request.auth != null;
    }
    
    // 订单数据访问规则
    match /orders/{orderId} {
      allow read, write: if request.auth != null;
    }
    
    // 默认规则：只有认证用户可以访问
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
