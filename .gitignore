# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
firebase-debug.log*
firebase-debug.*.log*

# Firebase cache
.firebase/

# Firebase config

# Uncomment this if you'd like others to create their own Firebase project.
# For a team working on the same Firebase project(s), it is recommended to leave
# it commented so all members can deploy to the same project(s) in .firebaserc.
# .firebaserc

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (http://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# dataconnect generated files
.dataconnect

# Additional Node.js ignores
jspm_packages/
lerna-debug.log*
.pnpm-debug.log*

# TypeScript
*.tsbuildinfo

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local

# Firebase specific additions
ui-debug.log
.runtimeconfig.json
firebase-export-*/
firestore-debug.log
database-debug.log
pubsub-debug.log
storage-debug.log

# Firebase Functions
functions/node_modules/
functions/.env
functions/.env.local

# Firebase Hosting
.firebase/hosting.*

# PGLite debug files
pglite-debug.log

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build and dist directories
build/
dist/
out/

# Test coverage
coverage/
*.lcov

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Local development files
.local/
local/
config.local.js
config.local.json

# Debug files
debug.log
error.log

# Archive files
*.zip
*.rar
*.tar.gz

# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port
