<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>培训班管理系统</title>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/11.10.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/11.10.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/11.10.0/firebase-firestore-compat.js"></script>

    <!-- CSS 样式 -->
    <link rel="stylesheet" href="css/styles.css">

    <!-- Font Awesome 图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 加载提示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
        <p>系统加载中...</p>
    </div>

    <!-- 登录页面 -->
    <div id="loginPage" class="page" style="display: none;">
        <div class="login-container">
            <div class="login-form">
                <h1><i class="fas fa-graduation-cap"></i> 培训班管理系统</h1>

                <!-- 演示模式提示 -->
                <div class="demo-notice">
                    <i class="fas fa-info-circle"></i>
                    <strong>演示模式</strong><br>
                    邮箱: <EMAIL><br>
                    密码: admin123
                </div>

                <form id="loginForm">
                    <div class="form-group">
                        <label for="email">邮箱</label>
                        <input type="email" id="email" required value="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="password">密码</label>
                        <input type="password" id="password" required value="admin123">
                    </div>
                    <button type="submit" class="btn btn-primary">登录</button>
                </form>

                <!-- 调试信息 -->
                <div id="debugInfo" style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px; font-size: 12px; color: #666;">
                    <strong>调试信息：</strong><br>
                    <span id="debugStatus">等待 Firebase 初始化...</span>
                    <br><br>
                    <button type="button" onclick="testAuth()" style="padding: 5px 10px; font-size: 11px; background: #28a745; color: white; border: none; border-radius: 3px; cursor: pointer;">
                        测试认证系统
                    </button>
                </div>
                <div id="loginError" class="error-message"></div>
            </div>
        </div>
    </div>

    <!-- 主应用页面 -->
    <div id="mainApp" class="page" style="display: none;">
        <!-- 导航栏 -->
        <nav class="navbar">
            <div class="nav-brand">
                <i class="fas fa-graduation-cap"></i>
                培训班管理系统
            </div>
            <div class="nav-menu">
                <a href="#" class="nav-item active" data-page="dashboard">
                    <i class="fas fa-tachometer-alt"></i> 仪表盘
                </a>
                <a href="#" class="nav-item" data-page="students">
                    <i class="fas fa-users"></i> 学生管理
                </a>
                <a href="#" class="nav-item" data-page="packages">
                    <i class="fas fa-box"></i> 课包管理
                </a>
                <a href="#" class="nav-item" data-page="courses">
                    <i class="fas fa-clock"></i> 课时管理
                </a>
                <a href="#" class="nav-item" data-page="orders">
                    <i class="fas fa-shopping-cart"></i> 订单管理
                </a>
            </div>
            <div class="nav-user">
                <span id="userEmail"></span>
                <button id="logoutBtn" class="btn btn-outline">
                    <i class="fas fa-sign-out-alt"></i> 退出
                </button>
            </div>
        </nav>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 仪表盘页面 -->
            <div id="dashboardPage" class="content-page active">
                <div class="page-header">
                    <h2>仪表盘</h2>
                </div>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalStudents">0</h3>
                            <p>总学生数</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="dashboardTotalHours">0</h3>
                            <p>总课时数</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalOrders">0</h3>
                            <p>总订单数</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalRevenue">¥0</h3>
                            <p>总收入</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 学生管理页面 -->
            <div id="studentsPage" class="content-page">
                <div class="page-header">
                    <h2>学生管理</h2>
                    <button id="addStudentBtn" class="btn btn-primary">
                        <i class="fas fa-plus"></i> 添加学生
                    </button>
                </div>
                <div class="table-container">
                    <table id="studentsTable" class="data-table">
                        <thead>
                            <tr>
                                <th>姓名</th>
                                <th>电话</th>
                                <th>邮箱</th>
                                <th>年级</th>
                                <th>状态</th>
                                <th>注册时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>

            <!-- 课包管理页面 -->
            <div id="packagesPage" class="content-page">
                <div class="page-header">
                    <h2>课包管理</h2>
                    <button id="addPackageBtn" class="btn btn-primary">
                        <i class="fas fa-plus"></i> 添加课包
                    </button>
                </div>
                <div class="table-container">
                    <table id="packagesTable" class="data-table">
                        <thead>
                            <tr>
                                <th>课包名称</th>
                                <th>课包金额</th>
                                <th>课时数</th>
                                <th>备注</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>

            <!-- 课时管理页面 -->
            <div id="coursesPage" class="content-page">
                <div class="page-header">
                    <h2>课时管理</h2>
                    <div class="header-actions">
                        <button id="consumeBtn" class="btn btn-warning">
                            <i class="fas fa-minus-circle"></i> 消课
                        </button>
                    </div>
                </div>

                <!-- 学生课时余额概览 -->
                <div class="balance-overview">
                    <h3>学生课时余额</h3>
                    <div id="studentBalances" class="balance-grid">
                        <!-- 动态生成学生余额卡片 -->
                    </div>
                </div>

                <!-- 课时流水记录 -->
                <div class="records-section">
                    <h3>课时流水记录</h3>
                    <div class="table-container">
                        <table id="courseRecordsTable" class="data-table">
                            <thead>
                                <tr>
                                    <th>学生姓名</th>
                                    <th>操作类型</th>
                                    <th>课时数</th>
                                    <th>课包名称</th>
                                    <th>操作日期</th>
                                    <th>备注</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 订单管理页面 -->
            <div id="ordersPage" class="content-page">
                <div class="page-header">
                    <h2>订单管理</h2>
                    <button id="addOrderBtn" class="btn btn-primary">
                        <i class="fas fa-plus"></i> 创建订单
                    </button>
                </div>
                <div class="table-container">
                    <table id="ordersTable" class="data-table">
                        <thead>
                            <tr>
                                <th>订单号</th>
                                <th>学生姓名</th>
                                <th>订单类型</th>
                                <th>金额</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <!-- 模态框容器 -->
    <div id="modalContainer"></div>

    <!-- JavaScript 文件 -->
    <script src="js/firebase-config.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/students.js"></script>
    <script src="js/packages.js"></script>
    <script src="js/courses.js"></script>
    <script src="js/orders.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
