// 主应用类
class TrainingManagementApp {
    constructor() {
        this.isInitialized = false;
        this.init();
    }

    init() {
        // 等待 Firebase 准备就绪
        window.addEventListener('firebaseReady', () => {
            this.onFirebaseReady();
        });

        // 设置全局错误处理
        this.setupErrorHandling();
        
        // 设置页面可见性变化监听
        this.setupVisibilityChange();
    }

    // Firebase 准备就绪后的初始化
    onFirebaseReady() {
        if (this.isInitialized) return;
        
        console.log('培训班管理系统初始化中...');
        
        // 初始化各个模块
        this.initializeModules();
        
        // 设置开发环境的测试数据
        if (this.isDevelopment()) {
            this.setupDevelopmentEnvironment();
        }
        
        this.isInitialized = true;
        console.log('培训班管理系统初始化完成');
    }

    // 初始化各个模块
    initializeModules() {
        // 所有模块都已经在各自的文件中初始化了
        // 这里可以添加模块间的协调逻辑
        
        // 设置模块间的事件通信
        this.setupModuleCommunication();
    }

    // 设置模块间通信
    setupModuleCommunication() {
        // 当学生被删除时，清理相关的课时包和订单
        window.addEventListener('studentDeleted', (e) => {
            this.handleStudentDeleted(e.detail.studentId);
        });

        // 当课时包被购买时，自动创建订单
        window.addEventListener('coursePackagePurchased', (e) => {
            this.handleCoursePackagePurchased(e.detail);
        });
    }

    // 处理学生删除事件
    async handleStudentDeleted(studentId) {
        try {
            // 这里可以添加清理逻辑
            console.log(`学生 ${studentId} 已删除，执行清理操作`);
        } catch (error) {
            console.error('处理学生删除事件失败:', error);
        }
    }

    // 处理课时包购买事件
    async handleCoursePackagePurchased(packageData) {
        try {
            // 自动创建对应的订单
            console.log('课时包购买事件:', packageData);
        } catch (error) {
            console.error('处理课时包购买事件失败:', error);
        }
    }

    // 设置全局错误处理
    setupErrorHandling() {
        // 捕获未处理的 Promise 错误
        window.addEventListener('unhandledrejection', (event) => {
            console.error('未处理的 Promise 错误:', event.reason);
            this.showErrorNotification('系统发生错误，请刷新页面重试');
        });

        // 捕获 JavaScript 错误
        window.addEventListener('error', (event) => {
            console.error('JavaScript 错误:', event.error);
            this.showErrorNotification('系统发生错误，请刷新页面重试');
        });
    }

    // 设置页面可见性变化监听
    setupVisibilityChange() {
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.isInitialized) {
                // 页面重新可见时，刷新数据
                this.refreshData();
            }
        });
    }

    // 刷新数据
    refreshData() {
        // 刷新当前页面的数据
        const currentPage = window.navigationManager?.getCurrentPage();
        
        switch (currentPage) {
            case 'dashboard':
                window.dashboardManager?.refresh();
                break;
            case 'students':
                window.studentManager?.loadStudents();
                break;
            case 'packages':
                window.packageManager?.loadPackages();
                break;
            case 'courses':
                window.courseManager?.loadCoursePackages();
                break;
            case 'orders':
                window.orderManager?.loadOrders();
                break;
        }
    }

    // 显示错误通知
    showErrorNotification(message) {
        // 创建简单的错误通知
        const notification = document.createElement('div');
        notification.className = 'error-notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #dc3545;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 300px;
            font-size: 14px;
        `;

        document.body.appendChild(notification);

        // 3秒后自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    // 检查是否为开发环境
    isDevelopment() {
        return location.hostname === 'localhost' || location.hostname === '127.0.0.1';
    }

    // 设置开发环境
    setupDevelopmentEnvironment() {
        console.log('开发环境检测到，设置测试功能...');
        
        // 添加创建管理员账户的功能
        window.createAdminAccount = async (email = '<EMAIL>', password = 'admin123') => {
            try {
                const user = await window.authManager.createAdminAccount(email, password);
                console.log('管理员账户创建成功:', user.email);
                alert(`管理员账户创建成功！\n邮箱: ${email}\n密码: ${password}`);
            } catch (error) {
                console.error('创建管理员账户失败:', error);
                alert('创建管理员账户失败: ' + error.message);
            }
        };

        // 添加创建测试数据的功能
        window.createTestData = async () => {
            try {
                await this.createTestStudents();
                await this.createTestCoursePackages();
                await this.createTestOrders();
                alert('测试数据创建成功！');
            } catch (error) {
                console.error('创建测试数据失败:', error);
                alert('创建测试数据失败: ' + error.message);
            }
        };

        console.log('开发环境功能已设置：');
        console.log('- 使用 createAdminAccount() 创建管理员账户');
        console.log('- 使用 createTestData() 创建测试数据');
    }

    // 创建测试学生数据
    async createTestStudents() {
        const studentsService = new DatabaseService('students');
        const testStudents = [
            {
                name: '张三',
                phone: '***********',
                email: '<EMAIL>',
                grade: '高一',
                status: 'active',
                notes: '数学基础较好'
            },
            {
                name: '李四',
                phone: '***********',
                email: '<EMAIL>',
                grade: '初三',
                status: 'active',
                notes: '英语需要加强'
            },
            {
                name: '王五',
                phone: '***********',
                email: '<EMAIL>',
                grade: '高二',
                status: 'inactive',
                notes: '暂时休学'
            }
        ];

        for (const student of testStudents) {
            await studentsService.add(student);
        }
    }

    // 创建测试课时包数据
    async createTestCoursePackages() {
        const studentsService = new DatabaseService('students');
        const coursePackagesService = new DatabaseService('coursePackages');
        
        const students = await studentsService.getAll();
        if (students.length === 0) return;

        const testPackages = [
            {
                studentId: students[0].id,
                packageName: '数学一对一课时包',
                totalHours: 20,
                usedHours: 5,
                remainingHours: 15,
                pricePerHour: 100,
                totalAmount: 2000,
                purchaseDate: window.firebaseService.timestamp(),
                notes: '高一数学辅导'
            },
            {
                studentId: students[1].id,
                packageName: '英语小班课时包',
                totalHours: 30,
                usedHours: 10,
                remainingHours: 20,
                pricePerHour: 80,
                totalAmount: 2400,
                purchaseDate: window.firebaseService.timestamp(),
                notes: '初三英语提升'
            }
        ];

        for (const pkg of testPackages) {
            await coursePackagesService.add(pkg);
        }
    }

    // 创建测试订单数据
    async createTestOrders() {
        const studentsService = new DatabaseService('students');
        const ordersService = new DatabaseService('orders');
        
        const students = await studentsService.getAll();
        if (students.length === 0) return;

        const testOrders = [
            {
                studentId: students[0].id,
                orderNumber: Utils.generateOrderNumber(),
                orderType: 'course_package',
                description: '数学一对一课时包 20课时',
                totalAmount: 2000,
                status: 'paid',
                paymentMethod: 'wechat',
                paidAt: window.firebaseService.timestamp(),
                notes: '已付款'
            },
            {
                studentId: students[1].id,
                orderNumber: Utils.generateOrderNumber(),
                orderType: 'course_package',
                description: '英语小班课时包 30课时',
                totalAmount: 2400,
                status: 'pending',
                paymentMethod: 'alipay',
                notes: '待付款'
            }
        ];

        for (const order of testOrders) {
            await ordersService.add(order);
        }
    }
}

// 创建应用实例
window.app = new TrainingManagementApp();

// 测试认证系统的全局函数
window.testAuth = function() {
    console.log('=== 测试认证系统 ===');

    const firebaseService = window.firebaseService;
    const authManager = window.authManager;

    console.log('Firebase Service:', firebaseService);
    console.log('Auth Manager:', authManager);

    if (firebaseService) {
        const auth = firebaseService.getAuth();
        console.log('Auth Object:', auth);

        if (auth && auth.signInWithEmailAndPassword) {
            console.log('认证方法存在，尝试直接调用...');

            auth.signInWithEmailAndPassword('<EMAIL>', 'admin123')
                .then(result => {
                    console.log('直接认证成功:', result);
                })
                .catch(error => {
                    console.error('直接认证失败:', error);
                });
        } else {
            console.error('认证方法不存在');
        }
    } else {
        console.error('Firebase Service 不存在');
    }
};

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('培训班管理系统启动中...');
});
