// 课时管理类
class CourseManager extends DatabaseService {
    constructor() {
        super('coursePackages');
        this.studentsCache = new Map();
        this.init();
    }

    init() {
        window.addEventListener('firebaseReady', () => {
            this.setupEventListeners();
            this.loadCoursePackages();
        });
    }

    // 设置事件监听器
    setupEventListeners() {
        // 添加课时包按钮
        const addCoursePackageBtn = document.getElementById('addCoursePackageBtn');
        if (addCoursePackageBtn) {
            addCoursePackageBtn.addEventListener('click', () => {
                this.showAddCoursePackageModal();
            });
        }

        // 监听页面切换事件
        window.addEventListener('pageChanged', (e) => {
            if (e.detail.page === 'courses') {
                this.loadCoursePackages();
            }
        });
    }

    // 加载课时包列表
    async loadCoursePackages() {
        try {
            const coursePackages = await this.getAll('createdAt', 'desc');
            await this.loadStudentsCache();
            this.renderCoursePackagesTable(coursePackages);
        } catch (error) {
            console.error('加载课时包列表失败:', error);
            alert('加载课时包列表失败，请刷新页面重试');
        }
    }

    // 加载学生缓存
    async loadStudentsCache() {
        try {
            const studentsService = new DatabaseService('students');
            const students = await studentsService.getAll();
            
            this.studentsCache.clear();
            students.forEach(student => {
                this.studentsCache.set(student.id, student);
            });
        } catch (error) {
            console.error('加载学生数据失败:', error);
        }
    }

    // 渲染课时包表格
    renderCoursePackagesTable(coursePackages) {
        const tbody = document.querySelector('#coursePackagesTable tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (coursePackages.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" style="text-align: center; padding: 40px; color: #666;">
                        暂无课时包数据
                    </td>
                </tr>
            `;
            return;
        }

        coursePackages.forEach(coursePackage => {
            const student = this.studentsCache.get(coursePackage.studentId);
            const studentName = student ? student.name : '未知学生';
            
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${studentName}</td>
                <td>${coursePackage.packageName || ''}</td>
                <td>${coursePackage.totalHours || 0}</td>
                <td>${coursePackage.usedHours || 0}</td>
                <td>${coursePackage.remainingHours || coursePackage.totalHours || 0}</td>
                <td>${Utils.formatCurrency(coursePackage.pricePerHour)}</td>
                <td>${window.firebaseService.formatTimestamp(coursePackage.purchaseDate)}</td>
                <td>
                    <button class="btn btn-sm btn-success" onclick="courseManager.useHours('${coursePackage.id}')">
                        <i class="fas fa-clock"></i> 消课
                    </button>
                    <button class="btn btn-sm btn-outline" onclick="courseManager.editCoursePackage('${coursePackage.id}')">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="courseManager.deleteCoursePackage('${coursePackage.id}', '${coursePackage.packageName}')">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // 显示添加课时包模态框
    async showAddCoursePackageModal() {
        await this.loadStudentsCache();
        this.showCoursePackageModal();
    }

    // 编辑课时包
    async editCoursePackage(packageId) {
        try {
            const coursePackage = await this.get(packageId);
            if (coursePackage) {
                await this.loadStudentsCache();
                this.showCoursePackageModal(coursePackage);
            }
        } catch (error) {
            console.error('获取课时包信息失败:', error);
            alert('获取课时包信息失败');
        }
    }

    // 删除课时包
    async deleteCoursePackage(packageId, packageName) {
        if (!confirm(`确定要删除课时包 "${packageName}" 吗？此操作不可恢复。`)) {
            return;
        }

        try {
            await this.delete(packageId);
            alert('课时包删除成功');
            this.loadCoursePackages();
        } catch (error) {
            console.error('删除课时包失败:', error);
            alert('删除课时包失败，请重试');
        }
    }

    // 消课操作
    async useHours(packageId) {
        try {
            const coursePackage = await this.get(packageId);
            if (!coursePackage) {
                alert('课时包不存在');
                return;
            }

            const remainingHours = coursePackage.remainingHours || (coursePackage.totalHours - (coursePackage.usedHours || 0));
            
            if (remainingHours <= 0) {
                alert('课时包已用完');
                return;
            }

            const hoursToUse = prompt(`剩余课时：${remainingHours}，请输入要消耗的课时数：`, '1');
            
            if (hoursToUse === null) return; // 用户取消
            
            const hours = parseFloat(hoursToUse);
            if (isNaN(hours) || hours <= 0) {
                alert('请输入有效的课时数');
                return;
            }

            if (hours > remainingHours) {
                alert('消耗课时数不能超过剩余课时数');
                return;
            }

            // 更新课时包
            const newUsedHours = (coursePackage.usedHours || 0) + hours;
            const newRemainingHours = (coursePackage.totalHours || 0) - newUsedHours;

            await this.update(packageId, {
                usedHours: newUsedHours,
                remainingHours: newRemainingHours
            });

            // 记录课时使用记录
            await this.recordClassUsage(packageId, coursePackage.studentId, hours);

            alert(`成功消耗 ${hours} 课时`);
            this.loadCoursePackages();
        } catch (error) {
            console.error('消课失败:', error);
            alert('消课失败，请重试');
        }
    }

    // 记录课时使用
    async recordClassUsage(packageId, studentId, hours) {
        try {
            const classRecordsService = new DatabaseService('classRecords');
            await classRecordsService.add({
                packageId,
                studentId,
                duration: hours,
                classDate: window.firebaseService.timestamp(),
                notes: '课时消耗记录'
            });
        } catch (error) {
            console.error('记录课时使用失败:', error);
        }
    }

    // 显示课时包模态框
    showCoursePackageModal(coursePackage = null) {
        const isEdit = !!coursePackage;
        const modalTitle = isEdit ? '编辑课时包' : '添加课时包';
        
        // 生成学生选项
        let studentOptions = '<option value="">请选择学生</option>';
        this.studentsCache.forEach((student, id) => {
            const selected = coursePackage?.studentId === id ? 'selected' : '';
            studentOptions += `<option value="${id}" ${selected}>${student.name}</option>`;
        });
        
        const modalHtml = `
            <div class="modal-overlay" id="coursePackageModal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>${modalTitle}</h3>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <form id="coursePackageForm" class="modal-body" onsubmit="return false;">
                        <div class="form-group">
                            <label for="packageStudentId">学生 *</label>
                            <select id="packageStudentId" required>
                                ${studentOptions}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="packageName">课时包名称 *</label>
                            <input type="text" id="packageName" required value="${coursePackage?.packageName || ''}" placeholder="例如：数学一对一课时包">
                        </div>
                        <div class="form-group">
                            <label for="totalHours">总课时数 *</label>
                            <input type="number" id="totalHours" name="totalHours" required min="1" step="0.5" value="${coursePackage?.totalHours || ''}" placeholder="例如：20">
                        </div>
                        <div class="form-group">
                            <label for="pricePerHour">单价（元/课时）*</label>
                            <input type="number" id="pricePerHour" required min="0" step="0.01" value="${coursePackage?.pricePerHour || ''}" placeholder="例如：100">
                        </div>
                        <div class="form-group">
                            <label for="packageNotes">备注</label>
                            <textarea id="packageNotes" rows="3" placeholder="课时包备注信息">${coursePackage?.notes || ''}</textarea>
                        </div>
                    </form>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">
                            取消
                        </button>
                        <button type="button" class="btn btn-primary" onclick="courseManager.saveCoursePackage('${coursePackage?.id || ''}')">
                            ${isEdit ? '更新' : '添加'}
                        </button>
                    </div>
                </div>
            </div>
        `;

        const modalContainer = document.getElementById('modalContainer');
        modalContainer.innerHTML = modalHtml;
    }

    // 保存课时包信息
    async saveCoursePackage(packageId) {
        // 获取输入值 - 直接使用最可靠的选择器
        const totalHoursInput = document.querySelector('#coursePackageForm input[name="totalHours"]') ||
                               document.querySelector('#coursePackageForm input[type="number"]:first-of-type');
        const pricePerHourInput = document.querySelector('#coursePackageForm input[name="pricePerHour"]') ||
                                 document.querySelector('#coursePackageForm input[type="number"]:last-of-type');

        console.log('=== 最终获取的输入框 ===');
        console.log('总课时输入框:', totalHoursInput);
        console.log('总课时值:', totalHoursInput?.value);
        console.log('单价输入框:', pricePerHourInput);
        console.log('单价值:', pricePerHourInput?.value);

        // 修复数据解析逻辑
        const totalHoursValue = totalHoursInput?.value?.trim();
        const pricePerHourValue = pricePerHourInput?.value?.trim();

        const packageData = {
            studentId: document.getElementById('packageStudentId').value,
            packageName: document.getElementById('packageName').value.trim(),
            totalHours: totalHoursValue ? parseFloat(totalHoursValue) : 0,
            pricePerHour: pricePerHourValue ? parseFloat(pricePerHourValue) : 0,
            notes: document.getElementById('packageNotes').value.trim()
        };

        console.log('解析后的数据:', packageData);

        // 验证必填字段
        if (!packageData.studentId) {
            alert('请选择学生');
            return;
        }
        if (!packageData.packageName) {
            alert('请输入课时包名称');
            return;
        }
        if (isNaN(packageData.totalHours) || packageData.totalHours <= 0) {
            alert('请输入有效的总课时数');
            return;
        }
        console.log('单价验证检查:');
        console.log('- 值:', packageData.pricePerHour);
        console.log('- 类型:', typeof packageData.pricePerHour);
        console.log('- isNaN:', isNaN(packageData.pricePerHour));
        console.log('- < 0:', packageData.pricePerHour < 0);
        console.log('- 条件结果:', isNaN(packageData.pricePerHour) || packageData.pricePerHour < 0);

        if (isNaN(packageData.pricePerHour) || packageData.pricePerHour < 0) {
            alert('请输入有效的单价');
            console.log('单价验证失败:', packageData.pricePerHour);
            return;
        }

        console.log('开始保存课时包...');
        console.log('Firebase 服务状态:', window.firebaseService);
        console.log('数据库集合:', this.collection);

        try {
            if (packageId) {
                // 更新课时包
                console.log('更新课时包:', packageId, packageData);
                await this.update(packageId, packageData);
                alert('课时包信息更新成功');
            } else {
                // 添加课时包
                packageData.usedHours = 0;
                packageData.remainingHours = packageData.totalHours;
                packageData.totalAmount = packageData.totalHours * packageData.pricePerHour;
                packageData.purchaseDate = window.firebaseService.timestamp();

                console.log('准备添加课时包:', packageData);
                await this.add(packageData);
                console.log('课时包添加成功');
                alert('课时包添加成功');
            }

            // 关闭模态框
            document.getElementById('coursePackageModal').remove();

            // 重新加载课时包列表
            this.loadCoursePackages();
        } catch (error) {
            console.error('保存课时包信息失败:', error);
            console.error('错误详情:', error.message);
            console.error('错误堆栈:', error.stack);
            alert('保存失败，请重试: ' + error.message);
        }
    }
}

// 创建全局实例
window.courseManager = new CourseManager();
