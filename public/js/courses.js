// 课时管理类
class CourseManager extends DatabaseService {
    constructor() {
        super('courseRecords');
        this.studentsCache = new Map();
        this.packagesCache = new Map();
        this.courseRecords = [];
        console.log('CourseManager 构造函数执行');
        this.init();
    }

    init() {
        console.log('CourseManager 初始化开始');
        try {
            window.addEventListener('firebaseReady', () => {
                console.log('Firebase 就绪，初始化课时管理');
                this.setupEventListeners();
                if (window.navigationManager?.getCurrentPage() === 'courses') {
                    this.loadCourseData();
                }
            });
            console.log('CourseManager 初始化完成');
        } catch (error) {
            console.error('CourseManager 初始化失败:', error);
        }
    }

    // 设置事件监听器
    setupEventListeners() {
        console.log('设置课时管理事件监听器');

        // 延迟设置消课按钮事件，确保DOM已加载
        setTimeout(() => {
            const consumeBtn = document.getElementById('consumeBtn');
            console.log('查找消课按钮:', consumeBtn);
            if (consumeBtn) {
                console.log('设置消课按钮事件监听器');
                consumeBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('消课按钮被点击');
                    this.showConsumeModal();
                });
            } else {
                console.warn('未找到消课按钮元素');
            }
        }, 100);

        // 监听页面切换事件
        window.addEventListener('pageChanged', (e) => {
            console.log('页面切换事件:', e.detail.page);
            if (e.detail.page === 'courses') {
                console.log('切换到课时管理页面，开始加载数据...');
                this.loadCourseData();
            }
        });
    }

    // 加载课时数据（学生、课包、记录）
    async loadCourseData() {
        try {
            console.log('开始加载课时数据...');

            // 加载学生数据
            await this.loadStudentsCache();
            console.log('学生数据加载完成，数量:', this.studentsCache.size);

            // 加载课包数据
            await this.loadPackagesCache();
            console.log('课包数据加载完成，数量:', this.packagesCache.size);

            // 加载课时记录
            await this.loadCourseRecords();
            console.log('课时记录加载完成，数量:', this.courseRecords.length);

            // 渲染页面
            this.renderStudentBalances();

            console.log('课时数据加载完成');
        } catch (error) {
            console.error('加载课时数据失败:', error);
        }
    }

    // 兼容旧方法名
    async loadCoursePackages() {
        await this.loadCourseData();
    }

    // 加载学生缓存
    async loadStudentsCache() {
        try {
            const studentsService = new DatabaseService('students');
            const students = await studentsService.getAll();

            this.studentsCache.clear();
            students.forEach(student => {
                this.studentsCache.set(student.id, student);
            });
        } catch (error) {
            console.error('加载学生数据失败:', error);
        }
    }

    // 加载课包缓存
    async loadPackagesCache() {
        try {
            const packagesService = new DatabaseService('packages');
            const packages = await packagesService.getAll();

            this.packagesCache.clear();
            packages.forEach(pkg => {
                this.packagesCache.set(pkg.id, pkg);
            });
        } catch (error) {
            console.error('加载课包缓存失败:', error);
        }
    }

    // 加载课时记录
    async loadCourseRecords() {
        try {
            console.log('开始加载课时记录...');
            const records = await this.getAll();
            console.log('获取到课时记录:', records);

            // 缓存记录
            this.courseRecords = records || [];

            // 渲染表格
            this.renderCourseRecordsTable(this.courseRecords);
        } catch (error) {
            console.error('加载课时记录失败:', error);
            this.courseRecords = [];
        }
    }

    // 渲染课时包表格
    renderCoursePackagesTable(coursePackages) {
        const tbody = document.querySelector('#coursePackagesTable tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (coursePackages.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" style="text-align: center; padding: 40px; color: #666;">
                        暂无课时包数据
                    </td>
                </tr>
            `;
            return;
        }

        coursePackages.forEach(coursePackage => {
            const student = this.studentsCache.get(coursePackage.studentId);
            const studentName = student ? student.name : '未知学生';
            
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${studentName}</td>
                <td>${coursePackage.packageName || ''}</td>
                <td>${coursePackage.totalHours || 0}</td>
                <td>${coursePackage.usedHours || 0}</td>
                <td>${coursePackage.remainingHours || coursePackage.totalHours || 0}</td>
                <td>${Utils.formatCurrency(coursePackage.pricePerHour)}</td>
                <td>${window.firebaseService.formatTimestamp(coursePackage.purchaseDate)}</td>
                <td>
                    <button class="btn btn-sm btn-success" onclick="courseManager.useHours('${coursePackage.id}')">
                        <i class="fas fa-clock"></i> 消课
                    </button>
                    <button class="btn btn-sm btn-outline" onclick="courseManager.editCoursePackage('${coursePackage.id}')">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="courseManager.deleteCoursePackage('${coursePackage.id}', '${coursePackage.packageName}')">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // 显示添加课时包模态框
    async showAddCoursePackageModal() {
        await this.loadStudentsCache();
        this.showCoursePackageModal();
    }

    // 编辑课时包
    async editCoursePackage(packageId) {
        try {
            const coursePackage = await this.get(packageId);
            if (coursePackage) {
                await this.loadStudentsCache();
                this.showCoursePackageModal(coursePackage);
            }
        } catch (error) {
            console.error('获取课时包信息失败:', error);
            alert('获取课时包信息失败');
        }
    }

    // 删除课时包
    async deleteCoursePackage(packageId, packageName) {
        if (!confirm(`确定要删除课时包 "${packageName}" 吗？此操作不可恢复。`)) {
            return;
        }

        try {
            await this.delete(packageId);
            alert('课时包删除成功');
            this.loadCoursePackages();
        } catch (error) {
            console.error('删除课时包失败:', error);
            alert('删除课时包失败，请重试');
        }
    }

    // 消课操作
    async useHours(packageId) {
        try {
            const coursePackage = await this.get(packageId);
            if (!coursePackage) {
                alert('课时包不存在');
                return;
            }

            const remainingHours = coursePackage.remainingHours || (coursePackage.totalHours - (coursePackage.usedHours || 0));
            
            if (remainingHours <= 0) {
                alert('课时包已用完');
                return;
            }

            const remainingMinutes = remainingHours * this.MINUTES_PER_HOUR;
            const hoursToUse = prompt(`剩余课时：${remainingHours} 课时 (${remainingMinutes}分钟)\n请输入要消耗的课时数：`, '1');
            
            if (hoursToUse === null) return; // 用户取消
            
            const hours = parseFloat(hoursToUse);
            if (isNaN(hours) || hours <= 0) {
                alert('请输入有效的课时数');
                return;
            }

            if (hours > remainingHours) {
                alert('消耗课时数不能超过剩余课时数');
                return;
            }

            // 更新课时包
            const newUsedHours = (coursePackage.usedHours || 0) + hours;
            const newRemainingHours = (coursePackage.totalHours || 0) - newUsedHours;

            await this.update(packageId, {
                usedHours: newUsedHours,
                remainingHours: newRemainingHours
            });

            // 记录课时使用记录
            await this.recordClassUsage(packageId, coursePackage.studentId, hours);

            const consumedMinutes = hours * this.MINUTES_PER_HOUR;
            alert(`成功消耗 ${hours} 课时 (${consumedMinutes}分钟)`);
            this.loadCoursePackages();
        } catch (error) {
            console.error('消课失败:', error);
            alert('消课失败，请重试');
        }
    }

    // 记录课时使用
    async recordClassUsage(packageId, studentId, hours) {
        try {
            const classRecordsService = new DatabaseService('classRecords');
            await classRecordsService.add({
                packageId,
                studentId,
                duration: hours,
                classDate: window.firebaseService.timestamp(),
                notes: '课时消耗记录'
            });
        } catch (error) {
            console.error('记录课时使用失败:', error);
        }
    }

    // 显示课时包模态框
    showCoursePackageModal(coursePackage = null) {
        const isEdit = !!coursePackage;
        const modalTitle = isEdit ? '编辑课时包' : '添加课时包';
        
        // 生成学生选项
        let studentOptions = '<option value="">请选择学生</option>';
        this.studentsCache.forEach((student, id) => {
            const selected = coursePackage?.studentId === id ? 'selected' : '';
            studentOptions += `<option value="${id}" ${selected}>${student.name}</option>`;
        });
        
        const modalHtml = `
            <div class="modal-overlay" id="coursePackageModal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title">${modalTitle}</h3>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <form id="coursePackageForm" class="modal-body" onsubmit="return false;">
                        <div class="form-group">
                            <label for="packageStudentId">学生 *</label>
                            <select id="packageStudentId" required>
                                ${studentOptions}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="packageName">课时包名称 *</label>
                            <input type="text" id="packageName" required value="${coursePackage?.packageName || ''}" placeholder="例如：数学一对一课时包">
                        </div>
                        <div class="form-group">
                            <label for="totalHours">总课时数 *</label>
                            <input type="number" id="totalHours" name="totalHours" required min="1" step="0.5" value="${coursePackage?.totalHours || ''}" placeholder="例如：20">
                        </div>
                        <div class="form-group">
                            <label for="pricePerHour">单价（元/课时）*</label>
                            <input type="number" id="pricePerHour" required min="0" step="0.01" value="${coursePackage?.pricePerHour || ''}" placeholder="例如：100">
                        </div>
                        <div class="form-group">
                            <label for="packageNotes">备注</label>
                            <textarea id="packageNotes" rows="3" placeholder="课时包备注信息">${coursePackage?.notes || ''}</textarea>
                        </div>
                    </form>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">
                            取消
                        </button>
                        <button type="button" class="btn btn-primary" onclick="courseManager.saveCoursePackage('${coursePackage?.id || ''}')">
                            ${isEdit ? '更新' : '添加'}
                        </button>
                    </div>
                </div>
            </div>
        `;

        const modalContainer = document.getElementById('modalContainer');
        modalContainer.innerHTML = modalHtml;
    }

    // 保存课时包信息
    async saveCoursePackage(packageId) {
        // 获取输入值
        const totalHoursInput = document.getElementById('totalHours');
        const pricePerHourInput = document.getElementById('pricePerHour');

        console.log('总课时输入框:', totalHoursInput);
        console.log('总课时原始值:', totalHoursInput?.value);
        console.log('单价输入框:', pricePerHourInput);
        console.log('单价原始值:', pricePerHourInput?.value);

        const packageData = {
            studentId: document.getElementById('packageStudentId').value,
            packageName: document.getElementById('packageName').value.trim(),
            totalHours: parseFloat(totalHoursInput?.value || '0'),
            pricePerHour: parseFloat(pricePerHourInput?.value || '0'),
            notes: document.getElementById('packageNotes').value.trim()
        };

        console.log('解析后的数据:', packageData);

        // 验证必填字段
        if (!packageData.studentId) {
            alert('请选择学生');
            return;
        }
        if (!packageData.packageName) {
            alert('请输入课时包名称');
            return;
        }
        if (isNaN(packageData.totalHours) || packageData.totalHours <= 0) {
            alert('请输入有效的总课时数');
            return;
        }
        console.log('单价验证检查:');
        console.log('- 值:', packageData.pricePerHour);
        console.log('- 类型:', typeof packageData.pricePerHour);
        console.log('- isNaN:', isNaN(packageData.pricePerHour));
        console.log('- < 0:', packageData.pricePerHour < 0);
        console.log('- 条件结果:', isNaN(packageData.pricePerHour) || packageData.pricePerHour < 0);

        if (isNaN(packageData.pricePerHour) || packageData.pricePerHour < 0) {
            alert('请输入有效的单价');
            console.log('单价验证失败:', packageData.pricePerHour);
            return;
        }

        console.log('开始保存课时包...');
        console.log('Firebase 服务状态:', window.firebaseService);
        console.log('数据库集合:', this.collection);

        try {
            if (packageId) {
                // 更新课时包
                console.log('更新课时包:', packageId, packageData);
                await this.update(packageId, packageData);
                alert('课时包信息更新成功');
            } else {
                // 添加课时包
                packageData.usedHours = 0;
                packageData.remainingHours = packageData.totalHours;
                packageData.totalAmount = packageData.totalHours * packageData.pricePerHour;
                packageData.purchaseDate = window.firebaseService.timestamp();

                console.log('准备添加课时包:', packageData);
                await this.add(packageData);
                console.log('课时包添加成功');
                alert('课时包添加成功');
            }

            // 关闭模态框
            document.getElementById('coursePackageModal').remove();

            // 重新加载课时包列表
            this.loadCoursePackages();
        } catch (error) {
            console.error('保存课时包信息失败:', error);
            console.error('错误详情:', error.message);
            console.error('错误堆栈:', error.stack);
            alert('保存失败，请重试: ' + error.message);
        }
    }
}

    // 渲染学生课时余额
    renderStudentBalances() {
        console.log('渲染学生课时余额');
        const container = document.getElementById('studentBalances');
        if (!container) {
            console.warn('未找到学生余额容器元素');
            return;
        }

        container.innerHTML = '';

        // 计算每个学生的课时余额
        const studentBalances = this.calculateStudentBalances();

        studentBalances.forEach(balance => {
            const card = document.createElement('div');
            card.className = 'balance-card';

            let balanceClass = '';
            if (balance.remaining <= 0) {
                balanceClass = 'empty';
            } else if (balance.remaining <= 5) {
                balanceClass = 'low';
            }

            card.innerHTML = `
                <div class="student-name">${balance.studentName}</div>
                <div class="balance-info">
                    <span>剩余课时</span>
                    <span class="hours-remaining ${balanceClass}">${balance.remaining} 课时</span>
                </div>
            `;

            container.appendChild(card);
        });

        console.log('学生余额卡片渲染完成，数量:', studentBalances.length);
    }

    // 计算学生课时余额
    calculateStudentBalances() {
        console.log('计算学生课时余额');
        const balances = new Map();

        // 初始化所有学生的余额为0
        this.studentsCache.forEach((student, studentId) => {
            balances.set(studentId, {
                studentId,
                studentName: student.name,
                remaining: 0
            });
        });

        // 使用缓存的课时记录计算余额
        console.log('使用课时记录计算余额，记录数量:', this.courseRecords.length);
        this.courseRecords.forEach(record => {
            const balance = balances.get(record.studentId);
            if (balance) {
                if (record.type === 'recharge') {
                    balance.remaining += record.hours;
                    console.log(`学生 ${balance.studentName} 充值 ${record.hours} 课时`);
                } else if (record.type === 'consume') {
                    balance.remaining -= record.hours;
                    console.log(`学生 ${balance.studentName} 消课 ${record.hours} 课时`);
                }
            }
        });

        const result = Array.from(balances.values());
        console.log('计算出的学生余额:', result);
        return result;
    }

    // 渲染课时记录表格
    renderCourseRecordsTable(records) {
        console.log('渲染课时记录表格，记录数量:', records.length);
        const tbody = document.querySelector('#courseRecordsTable tbody');
        if (!tbody) {
            console.warn('未找到课时记录表格tbody元素');
            return;
        }

        tbody.innerHTML = '';

        if (!records || records.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" style="text-align: center; padding: 40px; color: #666;">
                        暂无课时记录
                    </td>
                </tr>
            `;
            return;
        }

        records.forEach((record, index) => {
            console.log(`渲染第${index + 1}条记录:`, record);
            const student = this.studentsCache.get(record.studentId);
            const pkg = this.packagesCache.get(record.packageId);

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${student?.name || '未知学生'}</td>
                <td>
                    <span class="operation-type ${record.type === 'recharge' ? 'recharge' : 'consume'}">
                        ${record.type === 'recharge' ? '充值' : '消课'}
                    </span>
                </td>
                <td>${record.type === 'recharge' ? '+' : '-'}${record.hours} 课时</td>
                <td>${pkg?.name || record.packageName || '-'}</td>
                <td>${window.firebaseService.formatTimestamp(record.createdAt)}</td>
                <td>${record.notes || '-'}</td>
            `;
            tbody.appendChild(row);
        });

        console.log('课时记录表格渲染完成');
    }



    // 显示消课模态框
    showConsumeModal() {
        console.log('显示消课模态框');
        console.log('学生缓存:', this.studentsCache);

        // 生成学生选项
        let studentOptions = '<option value="">请选择学生</option>';
        this.studentsCache.forEach((student, id) => {
            studentOptions += `<option value="${id}">${student.name}</option>`;
        });

        const modalHtml = `
            <div class="modal-overlay" id="consumeModal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title">消课</h3>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <form id="consumeForm" class="modal-body" onsubmit="return false;">
                        <div class="form-group">
                            <label for="consumeStudentId">学生 *</label>
                            <select id="consumeStudentId" required onchange="courseManager.updateStudentBalance(this.value)">
                                ${studentOptions}
                            </select>
                        </div>
                        <div class="form-group">
                            <label>当前余额</label>
                            <div id="currentBalance" class="balance-display">请先选择学生</div>
                        </div>
                        <div class="form-group">
                            <label for="consumeHours">消课课时 *</label>
                            <input type="number" id="consumeHours" required min="0.5" step="0.5" placeholder="例如：1.5">
                            <small style="color: #666; margin-top: 5px; display: block;">每课时 = ${this.MINUTES_PER_HOUR} 分钟</small>
                        </div>
                        <div class="form-group">
                            <label for="consumeNotes">备注</label>
                            <textarea id="consumeNotes" rows="3" placeholder="消课备注信息"></textarea>
                        </div>
                    </form>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">
                            取消
                        </button>
                        <button type="button" class="btn btn-warning" onclick="courseManager.processConsume()">
                            确认消课
                        </button>
                    </div>
                </div>
            </div>
        `;

        const modalContainer = document.getElementById('modalContainer');
        modalContainer.innerHTML = modalHtml;
    }

    // 更新学生余额显示
    updateStudentBalance(studentId) {
        console.log('更新学生余额显示，学生ID:', studentId);
        const balanceDisplay = document.getElementById('currentBalance');
        if (!balanceDisplay || !studentId) {
            if (balanceDisplay) balanceDisplay.textContent = '请先选择学生';
            return;
        }

        const balance = this.getStudentBalance(studentId);
        balanceDisplay.textContent = `${balance} 课时`;
        balanceDisplay.className = `balance-display ${balance <= 0 ? 'empty' : balance <= 5 ? 'low' : ''}`;
    }

    // 获取学生课时余额
    getStudentBalance(studentId) {
        let balance = 0;

        this.courseRecords.forEach(record => {
            if (record.studentId === studentId) {
                if (record.type === 'recharge') {
                    balance += record.hours;
                } else if (record.type === 'consume') {
                    balance -= record.hours;
                }
            }
        });

        console.log(`学生 ${studentId} 的课时余额:`, balance);
        return balance;
    }

    // 处理消课
    async processConsume() {
        console.log('开始处理消课');
        try {
            const studentId = document.getElementById('consumeStudentId').value;
            const hours = parseFloat(document.getElementById('consumeHours').value);
            const notes = document.getElementById('consumeNotes').value.trim();

            console.log('消课参数:', { studentId, hours, notes });

            if (!studentId) {
                alert('请选择学生');
                return;
            }

            if (!hours || hours <= 0) {
                alert('请输入有效的课时数');
                return;
            }

            // 检查余额是否足够
            const currentBalance = this.getStudentBalance(studentId);
            console.log('当前余额:', currentBalance);
            if (hours > currentBalance) {
                alert(`课时余额不足！当前余额：${currentBalance} 课时`);
                return;
            }

            // 创建消课记录
            const consumeRecord = {
                studentId,
                type: 'consume',
                hours: hours,
                notes: notes || `消课 ${hours} 课时`,
                createdAt: window.firebaseService.timestamp()
            };

            console.log('准备保存消课记录:', consumeRecord);
            await this.add(consumeRecord);
            console.log('消课记录保存成功');

            alert(`消课成功！消耗了 ${hours} 课时`);

            // 关闭模态框并刷新数据
            const modal = document.querySelector('.modal-overlay');
            if (modal) modal.remove();

            this.loadCourseData();

        } catch (error) {
            console.error('消课失败:', error);
            alert('消课失败，请重试');
        }
    }

    // 调试方法
    debug() {
        console.log('=== 课时管理调试信息 ===');
        console.log('学生缓存:', this.studentsCache);
        console.log('课包缓存:', this.packagesCache);
        console.log('课时记录:', this.courseRecords);
        console.log('学生余额:', this.calculateStudentBalances());
        console.log('========================');
    }
}

// 确保在 DOM 加载完成后创建实例
console.log('准备创建 CourseManager 实例');

try {
    // 创建全局实例
    window.courseManager = new CourseManager();
    console.log('CourseManager 实例创建成功:', window.courseManager);

    // 调试函数
    window.debugCourseManager = function() {
        console.log('=== 课时管理调试信息 ===');
        console.log('courseManager 实例:', window.courseManager);
        console.log('当前页面:', window.navigationManager?.getCurrentPage());

        if (window.courseManager) {
            window.courseManager.debug();
            console.log('手动触发数据加载...');
            window.courseManager.loadCourseData();
        } else {
            console.error('courseManager 实例不存在');
        }
    };

    console.log('debugCourseManager 函数创建成功');
} catch (error) {
    console.error('创建 CourseManager 实例失败:', error);
}
