// 课包管理类
class PackageManager extends DatabaseService {
    constructor() {
        super('packages');
        this.init();
    }

    init() {
        window.addEventListener('firebaseReady', () => {
            this.loadPackages();
        });

        // 监听页面切换事件
        window.addEventListener('pageChanged', (e) => {
            if (e.detail.page === 'packages') {
                this.loadPackages();
            }
        });

        // 绑定添加课包按钮事件
        document.addEventListener('DOMContentLoaded', () => {
            const addPackageBtn = document.getElementById('addPackageBtn');
            if (addPackageBtn) {
                addPackageBtn.addEventListener('click', () => {
                    this.showPackageModal();
                });
            }
        });
    }

    // 加载课包列表
    async loadPackages() {
        try {
            const packages = await this.getAll();
            this.renderPackagesTable(packages);
        } catch (error) {
            console.error('加载课包列表失败:', error);
        }
    }

    // 渲染课包表格
    renderPackagesTable(packages) {
        const tbody = document.querySelector('#packagesTable tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        packages.forEach(pkg => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${pkg.name || ''}</td>
                <td>${Utils.formatCurrency(pkg.price)}</td>
                <td>${pkg.hours || 0} 课时</td>
                <td>${pkg.notes || '-'}</td>
                <td>${window.firebaseService.formatTimestamp(pkg.createdAt)}</td>
                <td>
                    <button class="btn btn-sm btn-outline" onclick="packageManager.editPackage('${pkg.id}')">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="packageManager.deletePackage('${pkg.id}')">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // 显示课包模态框
    showPackageModal(pkg = null) {
        const isEdit = !!pkg;
        const modalTitle = isEdit ? '编辑课包' : '添加课包';
        
        const modalHtml = `
            <div class="modal-overlay" id="packageModal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title">${modalTitle}</h3>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <form id="packageForm" class="modal-body" onsubmit="return false;">
                        <div class="form-group">
                            <label for="packageName">课包名称 *</label>
                            <input type="text" id="packageName" required value="${pkg?.name || ''}" placeholder="例如：数学一对一课包">
                        </div>
                        <div class="form-group">
                            <label for="packagePrice">课包金额（元）*</label>
                            <input type="number" id="packagePrice" required min="0" step="0.01" value="${pkg?.price || ''}" placeholder="例如：2000">
                        </div>
                        <div class="form-group">
                            <label for="packageHours">课时数 *</label>
                            <input type="number" id="packageHours" required min="1" step="0.5" value="${pkg?.hours || ''}" placeholder="例如：20">
                        </div>
                        <div class="form-group">
                            <label for="packageNotes">备注</label>
                            <textarea id="packageNotes" rows="3" placeholder="课包备注信息">${pkg?.notes || ''}</textarea>
                        </div>
                    </form>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">
                            取消
                        </button>
                        <button type="button" class="btn btn-primary" onclick="packageManager.savePackage('${pkg?.id || ''}')">
                            ${isEdit ? '更新' : '添加'}
                        </button>
                    </div>
                </div>
            </div>
        `;

        const modalContainer = document.getElementById('modalContainer');
        modalContainer.innerHTML = modalHtml;
    }

    // 保存课包信息
    async savePackage(packageId) {
        try {
            const packageData = {
                name: document.getElementById('packageName').value.trim(),
                price: parseFloat(document.getElementById('packagePrice').value || '0'),
                hours: parseFloat(document.getElementById('packageHours').value || '0'),
                notes: document.getElementById('packageNotes').value.trim()
            };

            // 验证数据
            if (!packageData.name) {
                alert('请输入课包名称');
                return;
            }

            if (packageData.price <= 0) {
                alert('请输入有效的课包金额');
                return;
            }

            if (packageData.hours <= 0) {
                alert('请输入有效的课时数');
                return;
            }

            if (packageId) {
                // 更新课包
                await this.update(packageId, packageData);
                alert('课包更新成功');
            } else {
                // 添加课包
                packageData.createdAt = window.firebaseService.timestamp();
                await this.add(packageData);
                alert('课包添加成功');
            }

            // 关闭模态框并刷新列表
            document.getElementById('packageModal').remove();
            this.loadPackages();

        } catch (error) {
            console.error('保存课包失败:', error);
            alert('保存课包失败，请重试');
        }
    }

    // 编辑课包
    async editPackage(packageId) {
        try {
            const pkg = await this.get(packageId);
            if (pkg) {
                this.showPackageModal(pkg);
            }
        } catch (error) {
            console.error('获取课包信息失败:', error);
            alert('获取课包信息失败');
        }
    }

    // 删除课包
    async deletePackage(packageId) {
        if (!confirm('确定要删除这个课包吗？')) {
            return;
        }

        try {
            await this.delete(packageId);
            alert('课包删除成功');
            this.loadPackages();
        } catch (error) {
            console.error('删除课包失败:', error);
            alert('删除课包失败，请重试');
        }
    }

    // 获取所有课包（供其他模块使用）
    async getAllPackages() {
        try {
            return await this.getAll();
        } catch (error) {
            console.error('获取课包列表失败:', error);
            return [];
        }
    }
}

// 创建全局课包管理器实例
const packageManager = new PackageManager();

// 工具类扩展
if (typeof Utils === 'undefined') {
    window.Utils = {};
}

Utils.formatCurrency = function(amount) {
    if (typeof amount !== 'number') {
        amount = parseFloat(amount) || 0;
    }
    return '¥' + amount.toFixed(2);
};
