// Firebase 配置和初始化
class FirebaseService {
    constructor() {
        this.db = null;
        this.auth = null;
        this.init();
    }

    init() {
        // 等待 Firebase SDK 加载完成
        document.addEventListener('DOMContentLoaded', () => {
            this.waitForFirebase();
        });
    }

    waitForFirebase() {
        if (typeof firebase !== 'undefined') {
            try {
                console.log('Firebase SDK 已加载，尝试初始化...');

                // 直接使用模拟模式进行演示
                console.log('跳过真实 Firebase 初始化，直接使用模拟模式');
                this.initMockMode();

            } catch (error) {
                console.error('Firebase 初始化失败:', error);
                // 如果 Firebase 初始化失败，使用模拟模式
                this.initMockMode();
            }
        } else {
            console.log('等待 Firebase SDK 加载...');
            setTimeout(() => this.waitForFirebase(), 100);
        }
    }

    // 模拟模式（用于演示）
    initMockMode() {
        console.log('启动模拟模式进行演示');
        this.updateDebugInfo('启动演示模式（无需真实 Firebase 项目）');

        // 创建模拟的认证状态管理
        this.mockUser = null;
        this.authCallbacks = [];

        // 创建模拟的认证对象
        this.auth = {
            onAuthStateChanged: (callback) => {
                this.authCallbacks.push(callback);
                // 立即调用回调，传入当前用户状态
                callback(this.mockUser);
                return () => {
                    // 返回取消订阅函数
                    const index = this.authCallbacks.indexOf(callback);
                    if (index > -1) {
                        this.authCallbacks.splice(index, 1);
                    }
                };
            },
            signInWithEmailAndPassword: async (email, password) => {
                console.log('模拟登录尝试:', { email, password: '***' });

                if (email === '<EMAIL>' && password === 'admin123') {
                    console.log('模拟登录成功');
                    const user = { email, uid: 'demo-user' };
                    this.mockUser = user;

                    console.log('通知认证状态监听器，用户数量:', this.authCallbacks.length);

                    // 通知所有监听器用户状态已改变
                    this.authCallbacks.forEach((callback, index) => {
                        console.log(`调用认证回调 ${index}`);
                        setTimeout(() => callback(user), 100);
                    });

                    return { user };
                }
                console.log('模拟登录失败：凭据不匹配');
                throw new Error('邮箱或密码错误');
            },
            signOut: async () => {
                this.mockUser = null;
                // 通知所有监听器用户已登出
                this.authCallbacks.forEach(callback => {
                    setTimeout(() => callback(null), 100);
                });
                return Promise.resolve();
            }
        };

        // 创建模拟的数据库对象
        this.db = new MockFirestore();

        this.onFirebaseReady();
    }

    onFirebaseReady() {
        // 更新调试信息
        this.updateDebugInfo('Firebase 已初始化，系统准备就绪');

        // 触发自定义事件，通知其他模块 Firebase 已准备就绪
        window.dispatchEvent(new CustomEvent('firebaseReady'));
    }

    // 更新调试信息
    updateDebugInfo(message) {
        const debugStatus = document.getElementById('debugStatus');
        if (debugStatus) {
            debugStatus.textContent = message;
        }
        console.log('Debug:', message);
    }

    // 获取 Firestore 实例
    getFirestore() {
        return this.db;
    }

    // 获取 Auth 实例
    getAuth() {
        return this.auth;
    }

    // 生成时间戳
    timestamp() {
        return firebase.firestore.FieldValue.serverTimestamp();
    }

    // 格式化时间戳
    formatTimestamp(timestamp) {
        if (!timestamp) return '';
        
        const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // 生成唯一ID
    generateId() {
        return this.db.collection('temp').doc().id;
    }
}

// 数据库操作基类
class DatabaseService {
    constructor(collectionName) {
        this.collectionName = collectionName;
        this.firebaseService = window.firebaseService;
    }

    get collection() {
        return this.firebaseService.getFirestore().collection(this.collectionName);
    }

    // 添加文档
    async add(data) {
        try {
            data.createdAt = this.firebaseService.timestamp();
            data.updatedAt = this.firebaseService.timestamp();
            
            const docRef = await this.collection.add(data);
            return { id: docRef.id, ...data };
        } catch (error) {
            console.error(`添加${this.collectionName}失败:`, error);
            throw error;
        }
    }

    // 更新文档
    async update(id, data) {
        try {
            data.updatedAt = this.firebaseService.timestamp();
            await this.collection.doc(id).update(data);
            return { id, ...data };
        } catch (error) {
            console.error(`更新${this.collectionName}失败:`, error);
            throw error;
        }
    }

    // 删除文档
    async delete(id) {
        try {
            await this.collection.doc(id).delete();
            return true;
        } catch (error) {
            console.error(`删除${this.collectionName}失败:`, error);
            throw error;
        }
    }

    // 获取单个文档
    async get(id) {
        try {
            const doc = await this.collection.doc(id).get();
            if (doc.exists) {
                return { id: doc.id, ...doc.data() };
            }
            return null;
        } catch (error) {
            console.error(`获取${this.collectionName}失败:`, error);
            throw error;
        }
    }

    // 获取所有文档
    async getAll(orderBy = 'createdAt', direction = 'desc') {
        try {
            const snapshot = await this.collection.orderBy(orderBy, direction).get();
            return snapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
        } catch (error) {
            console.error(`获取${this.collectionName}列表失败:`, error);
            throw error;
        }
    }

    // 查询文档
    async query(field, operator, value) {
        try {
            const snapshot = await this.collection.where(field, operator, value).get();
            return snapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
        } catch (error) {
            console.error(`查询${this.collectionName}失败:`, error);
            throw error;
        }
    }

    // 实时监听
    onSnapshot(callback, orderBy = 'createdAt', direction = 'desc') {
        return this.collection.orderBy(orderBy, direction).onSnapshot(snapshot => {
            const data = snapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
            callback(data);
        });
    }
}

// 工具函数
const Utils = {
    // 显示加载状态
    showLoading() {
        const loading = document.getElementById('loading');
        if (loading) loading.style.display = 'flex';
    },

    // 隐藏加载状态
    hideLoading() {
        const loading = document.getElementById('loading');
        if (loading) loading.style.display = 'none';
    },

    // 显示错误消息
    showError(message, containerId = 'loginError') {
        const errorEl = document.getElementById(containerId);
        if (errorEl) {
            errorEl.textContent = message;
            errorEl.style.display = 'block';
        }
    },

    // 隐藏错误消息
    hideError(containerId = 'loginError') {
        const errorEl = document.getElementById(containerId);
        if (errorEl) {
            errorEl.style.display = 'none';
        }
    },

    // 格式化货币
    formatCurrency(amount) {
        return `¥${parseFloat(amount || 0).toFixed(2)}`;
    },

    // 生成订单号
    generateOrderNumber() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hour = String(now.getHours()).padStart(2, '0');
        const minute = String(now.getMinutes()).padStart(2, '0');
        const second = String(now.getSeconds()).padStart(2, '0');
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        
        return `ORD${year}${month}${day}${hour}${minute}${second}${random}`;
    }
};

// 模拟 Firestore 类（用于演示）
class MockFirestore {
    constructor() {
        this.data = new Map();
        this.idCounter = 1;
    }

    collection(name) {
        return new MockCollection(name, this);
    }

    settings() {
        // 模拟设置方法
    }
}

class MockCollection {
    constructor(name, firestore) {
        this.name = name;
        this.firestore = firestore;
    }

    doc(id) {
        return new MockDocument(id || this.generateId(), this.name, this.firestore);
    }

    async add(data) {
        const id = this.generateId();
        const key = `${this.name}/${id}`;

        const docData = {
            ...data,
            createdAt: new Date(),
            updatedAt: new Date()
        };

        this.firestore.data.set(key, docData);
        console.log(`Mock数据已保存: ${key}`, docData);
        return { id, ...docData };
    }

    async get() {
        const docs = [];
        for (const [key, value] of this.firestore.data.entries()) {
            if (key.startsWith(`${this.name}/`)) {
                const id = key.split('/')[1];
                docs.push({
                    id,
                    data: () => value,
                    exists: true
                });
            }
        }

        return {
            docs,
            forEach: (callback) => docs.forEach(callback)
        };
    }

    where(field, operator, value) {
        return this; // 简化实现
    }

    orderBy(field, direction) {
        return this;
    }

    onSnapshot(callback) {
        // 模拟实时监听
        setTimeout(() => {
            this.get().then(snapshot => {
                const data = snapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));
                callback({ docs: snapshot.docs });
            });
        }, 100);

        return () => {}; // 返回取消订阅函数
    }

    generateId() {
        return `mock_${this.firestore.idCounter++}`;
    }
}

class MockDocument {
    constructor(id, collection, firestore) {
        this.id = id;
        this.collection = collection;
        this.firestore = firestore;
        this.key = `${collection}/${id}`;
    }

    async get() {
        const data = this.firestore.data.get(this.key);
        return {
            id: this.id,
            data: () => data || {},
            exists: !!data
        };
    }

    async update(data) {
        const existing = this.firestore.data.get(this.key) || {};
        const updated = {
            ...existing,
            ...data,
            updatedAt: new Date()
        };
        this.firestore.data.set(this.key, updated);
    }

    async delete() {
        this.firestore.data.delete(this.key);
    }
}

// 创建全局 Firebase 服务实例
window.firebaseService = new FirebaseService();
window.DatabaseService = DatabaseService;
window.Utils = Utils;
