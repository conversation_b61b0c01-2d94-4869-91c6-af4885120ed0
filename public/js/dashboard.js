// 仪表盘管理类
class DashboardManager {
    constructor() {
        this.stats = {
            totalStudents: 0,
            totalHours: 0,
            totalOrders: 0,
            totalRevenue: 0
        };
        this.init();
    }

    init() {
        window.addEventListener('firebaseReady', () => {
            this.loadDashboardData();
        });

        // 监听页面切换事件
        window.addEventListener('pageChanged', (e) => {
            if (e.detail.page === 'dashboard') {
                this.loadDashboardData();
            }
        });
    }

    // 加载仪表盘数据
    async loadDashboardData() {
        try {
            await Promise.all([
                this.loadStudentsStats(),
                this.loadCoursesStats(),
                this.loadOrdersStats()
            ]);
            
            this.updateStatsDisplay();
        } catch (error) {
            console.error('加载仪表盘数据失败:', error);
        }
    }

    // 加载学生统计数据
    async loadStudentsStats() {
        try {
            const studentsService = new DatabaseService('students');
            const students = await studentsService.getAll();
            
            this.stats.totalStudents = students.length;
        } catch (error) {
            console.error('加载学生统计失败:', error);
            this.stats.totalStudents = 0;
        }
    }

    // 加载课时统计数据
    async loadCoursesStats() {
        try {
            const coursePackagesService = new DatabaseService('coursePackages');
            const coursePackages = await coursePackagesService.getAll();
            
            this.stats.totalHours = coursePackages.reduce((total, pkg) => {
                return total + (pkg.totalHours || 0);
            }, 0);
        } catch (error) {
            console.error('加载课时统计失败:', error);
            this.stats.totalHours = 0;
        }
    }

    // 加载订单统计数据
    async loadOrdersStats() {
        try {
            const ordersService = new DatabaseService('orders');
            const orders = await ordersService.getAll();
            
            this.stats.totalOrders = orders.length;
            
            // 计算总收入（只计算已付款的订单）
            this.stats.totalRevenue = orders
                .filter(order => order.status === 'paid')
                .reduce((total, order) => {
                    return total + (order.totalAmount || 0);
                }, 0);
        } catch (error) {
            console.error('加载订单统计失败:', error);
            this.stats.totalOrders = 0;
            this.stats.totalRevenue = 0;
        }
    }

    // 更新统计数据显示
    updateStatsDisplay() {
        // 更新总学生数
        const totalStudentsEl = document.getElementById('totalStudents');
        if (totalStudentsEl) {
            this.animateNumber(totalStudentsEl, this.stats.totalStudents);
        }

        // 更新总课时数
        const totalHoursEl = document.getElementById('totalHours');
        if (totalHoursEl) {
            this.animateNumber(totalHoursEl, this.stats.totalHours);
        }

        // 更新总订单数
        const totalOrdersEl = document.getElementById('totalOrders');
        if (totalOrdersEl) {
            this.animateNumber(totalOrdersEl, this.stats.totalOrders);
        }

        // 更新总收入
        const totalRevenueEl = document.getElementById('totalRevenue');
        if (totalRevenueEl) {
            this.animateRevenue(totalRevenueEl, this.stats.totalRevenue);
        }
    }

    // 数字动画效果
    animateNumber(element, targetValue, duration = 1000) {
        const startValue = 0;
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用缓动函数
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentValue = Math.floor(startValue + (targetValue - startValue) * easeOutQuart);
            
            element.textContent = currentValue.toString();
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                element.textContent = targetValue.toString();
            }
        };

        requestAnimationFrame(animate);
    }

    // 收入动画效果
    animateRevenue(element, targetValue, duration = 1000) {
        const startValue = 0;
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用缓动函数
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentValue = startValue + (targetValue - startValue) * easeOutQuart;
            
            element.textContent = Utils.formatCurrency(currentValue);
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                element.textContent = Utils.formatCurrency(targetValue);
            }
        };

        requestAnimationFrame(animate);
    }

    // 获取统计数据
    getStats() {
        return { ...this.stats };
    }

    // 刷新仪表盘数据
    refresh() {
        this.loadDashboardData();
    }
}

// 创建全局实例
window.dashboardManager = new DashboardManager();
