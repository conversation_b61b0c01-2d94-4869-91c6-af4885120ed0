// 学生管理类
class StudentManager extends DatabaseService {
    constructor() {
        super('students');
        this.init();
    }

    init() {
        window.addEventListener('firebaseReady', () => {
            this.setupEventListeners();
            this.loadStudents();
        });
    }

    // 设置事件监听器
    setupEventListeners() {
        // 添加学生按钮
        const addStudentBtn = document.getElementById('addStudentBtn');
        if (addStudentBtn) {
            addStudentBtn.addEventListener('click', () => {
                this.showAddStudentModal();
            });
        }

        // 监听页面切换事件
        window.addEventListener('pageChanged', (e) => {
            if (e.detail.page === 'students') {
                this.loadStudents();
            }
        });
    }

    // 加载学生列表
    async loadStudents() {
        try {
            const students = await this.getAll('createdAt', 'desc');
            this.renderStudentsTable(students);
        } catch (error) {
            console.error('加载学生列表失败:', error);
            alert('加载学生列表失败，请刷新页面重试');
        }
    }

    // 渲染学生表格
    renderStudentsTable(students) {
        const tbody = document.querySelector('#studentsTable tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (students.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" style="text-align: center; padding: 40px; color: #666;">
                        暂无学生数据
                    </td>
                </tr>
            `;
            return;
        }

        students.forEach(student => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${student.name || ''}</td>
                <td>${student.phone || ''}</td>
                <td>${student.email || ''}</td>
                <td>${student.grade || ''}</td>
                <td>
                    <span class="status-badge status-${student.status || 'active'}">
                        ${this.getStatusText(student.status)}
                    </span>
                </td>
                <td>${window.firebaseService.formatTimestamp(student.createdAt)}</td>
                <td>
                    <button class="btn btn-sm btn-outline" onclick="studentManager.editStudent('${student.id}')">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="studentManager.deleteStudent('${student.id}', '${student.name}')">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // 获取状态文本
    getStatusText(status) {
        const statusMap = {
            'active': '在读',
            'inactive': '休学',
            'graduated': '毕业'
        };
        return statusMap[status] || '在读';
    }

    // 显示添加学生模态框
    showAddStudentModal() {
        this.showStudentModal();
    }

    // 编辑学生
    async editStudent(studentId) {
        try {
            const student = await this.get(studentId);
            if (student) {
                this.showStudentModal(student);
            }
        } catch (error) {
            console.error('获取学生信息失败:', error);
            alert('获取学生信息失败');
        }
    }

    // 删除学生
    async deleteStudent(studentId, studentName) {
        if (!confirm(`确定要删除学生 "${studentName}" 吗？此操作不可恢复。`)) {
            return;
        }

        try {
            await this.delete(studentId);
            alert('学生删除成功');
            this.loadStudents();
        } catch (error) {
            console.error('删除学生失败:', error);
            alert('删除学生失败，请重试');
        }
    }

    // 显示学生模态框
    showStudentModal(student = null) {
        const isEdit = !!student;
        const modalTitle = isEdit ? '编辑学生' : '添加学生';
        
        const modalHtml = `
            <div class="modal-overlay" id="studentModal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>${modalTitle}</h3>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <form id="studentForm" class="modal-body">
                        <div class="form-group">
                            <label for="studentName">姓名 *</label>
                            <input type="text" id="studentName" required value="${student?.name || ''}">
                        </div>
                        <div class="form-group">
                            <label for="studentPhone">电话</label>
                            <input type="tel" id="studentPhone" value="${student?.phone || ''}">
                        </div>
                        <div class="form-group">
                            <label for="studentEmail">邮箱</label>
                            <input type="email" id="studentEmail" value="${student?.email || ''}">
                        </div>
                        <div class="form-group">
                            <label for="studentGrade">年级</label>
                            <select id="studentGrade">
                                <option value="">请选择年级</option>
                                <option value="小学一年级" ${student?.grade === '小学一年级' ? 'selected' : ''}>小学一年级</option>
                                <option value="小学二年级" ${student?.grade === '小学二年级' ? 'selected' : ''}>小学二年级</option>
                                <option value="小学三年级" ${student?.grade === '小学三年级' ? 'selected' : ''}>小学三年级</option>
                                <option value="小学四年级" ${student?.grade === '小学四年级' ? 'selected' : ''}>小学四年级</option>
                                <option value="小学五年级" ${student?.grade === '小学五年级' ? 'selected' : ''}>小学五年级</option>
                                <option value="小学六年级" ${student?.grade === '小学六年级' ? 'selected' : ''}>小学六年级</option>
                                <option value="初一" ${student?.grade === '初一' ? 'selected' : ''}>初一</option>
                                <option value="初二" ${student?.grade === '初二' ? 'selected' : ''}>初二</option>
                                <option value="初三" ${student?.grade === '初三' ? 'selected' : ''}>初三</option>
                                <option value="高一" ${student?.grade === '高一' ? 'selected' : ''}>高一</option>
                                <option value="高二" ${student?.grade === '高二' ? 'selected' : ''}>高二</option>
                                <option value="高三" ${student?.grade === '高三' ? 'selected' : ''}>高三</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="studentStatus">状态</label>
                            <select id="studentStatus">
                                <option value="active" ${(student?.status || 'active') === 'active' ? 'selected' : ''}>在读</option>
                                <option value="inactive" ${student?.status === 'inactive' ? 'selected' : ''}>休学</option>
                                <option value="graduated" ${student?.status === 'graduated' ? 'selected' : ''}>毕业</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="studentNotes">备注</label>
                            <textarea id="studentNotes" rows="3" placeholder="学生备注信息">${student?.notes || ''}</textarea>
                        </div>
                    </form>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">
                            取消
                        </button>
                        <button type="submit" class="btn btn-primary" onclick="studentManager.saveStudent('${student?.id || ''}')">
                            ${isEdit ? '更新' : '添加'}
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 添加模态框样式
        if (!document.getElementById('modalStyles')) {
            const modalStyles = document.createElement('style');
            modalStyles.id = 'modalStyles';
            modalStyles.textContent = `
                .modal-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 1000;
                }
                .modal-content {
                    background: white;
                    border-radius: 10px;
                    width: 90%;
                    max-width: 500px;
                    max-height: 90vh;
                    overflow-y: auto;
                }
                .modal-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 20px;
                    border-bottom: 1px solid #eee;
                }
                .modal-header h3 {
                    margin: 0;
                    color: #333;
                }
                .modal-close {
                    background: none;
                    border: none;
                    font-size: 18px;
                    cursor: pointer;
                    color: #666;
                }
                .modal-body {
                    padding: 20px;
                }
                .modal-footer {
                    display: flex;
                    justify-content: flex-end;
                    gap: 10px;
                    padding: 20px;
                    border-top: 1px solid #eee;
                }
                .btn-sm {
                    padding: 5px 10px;
                    font-size: 12px;
                }
            `;
            document.head.appendChild(modalStyles);
        }

        const modalContainer = document.getElementById('modalContainer');
        modalContainer.innerHTML = modalHtml;
    }

    // 保存学生信息
    async saveStudent(studentId) {
        const form = document.getElementById('studentForm');
        const formData = new FormData(form);
        
        const studentData = {
            name: document.getElementById('studentName').value.trim(),
            phone: document.getElementById('studentPhone').value.trim(),
            email: document.getElementById('studentEmail').value.trim(),
            grade: document.getElementById('studentGrade').value,
            status: document.getElementById('studentStatus').value,
            notes: document.getElementById('studentNotes').value.trim()
        };

        // 验证必填字段
        if (!studentData.name) {
            alert('请输入学生姓名');
            return;
        }

        try {
            if (studentId) {
                // 更新学生
                await this.update(studentId, studentData);
                alert('学生信息更新成功');
            } else {
                // 添加学生
                await this.add(studentData);
                alert('学生添加成功');
            }

            // 关闭模态框
            document.getElementById('studentModal').remove();
            
            // 重新加载学生列表
            this.loadStudents();
        } catch (error) {
            console.error('保存学生信息失败:', error);
            alert('保存失败，请重试');
        }
    }
}

// 创建全局实例
window.studentManager = new StudentManager();
