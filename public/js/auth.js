// 认证管理类
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.init();
    }

    init() {
        // 等待 Firebase 准备就绪
        window.addEventListener('firebaseReady', () => {
            this.setupAuthListener();
            this.setupEventListeners();
        });
    }

    // 设置认证状态监听器
    setupAuthListener() {
        const auth = window.firebaseService.getAuth();
        
        auth.onAuthStateChanged((user) => {
            this.currentUser = user;
            
            if (user) {
                this.showMainApp();
                this.updateUserInfo();
            } else {
                this.showLoginPage();
            }
            
            Utils.hideLoading();
        });
    }

    // 设置事件监听器
    setupEventListeners() {
        // 登录表单提交
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin();
            });
        }

        // 退出登录按钮
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                this.handleLogout();
            });
        }
    }

    // 处理登录
    async handleLogin() {
        const email = document.getElementById('email').value.trim();
        const password = document.getElementById('password').value;

        if (!email || !password) {
            Utils.showError('请输入邮箱和密码');
            return;
        }

        try {
            Utils.hideError();
            Utils.showLoading();

            const auth = window.firebaseService.getAuth();
            await auth.signInWithEmailAndPassword(email, password);
            
            // 登录成功，onAuthStateChanged 会自动处理页面跳转
        } catch (error) {
            Utils.hideLoading();
            this.handleAuthError(error);
        }
    }

    // 处理退出登录
    async handleLogout() {
        try {
            const auth = window.firebaseService.getAuth();
            await auth.signOut();
            // 退出成功，onAuthStateChanged 会自动处理页面跳转
        } catch (error) {
            console.error('退出登录失败:', error);
            alert('退出登录失败，请重试');
        }
    }

    // 处理认证错误
    handleAuthError(error) {
        let message = '登录失败，请重试';
        
        switch (error.code) {
            case 'auth/user-not-found':
                message = '用户不存在';
                break;
            case 'auth/wrong-password':
                message = '密码错误';
                break;
            case 'auth/invalid-email':
                message = '邮箱格式不正确';
                break;
            case 'auth/user-disabled':
                message = '用户账户已被禁用';
                break;
            case 'auth/too-many-requests':
                message = '登录尝试次数过多，请稍后再试';
                break;
            case 'auth/network-request-failed':
                message = '网络连接失败，请检查网络';
                break;
            default:
                console.error('认证错误:', error);
        }
        
        Utils.showError(message);
    }

    // 显示登录页面
    showLoginPage() {
        const loginPage = document.getElementById('loginPage');
        const mainApp = document.getElementById('mainApp');
        
        if (loginPage) loginPage.style.display = 'block';
        if (mainApp) mainApp.style.display = 'none';
        
        // 清空登录表单
        const loginForm = document.getElementById('loginForm');
        if (loginForm) loginForm.reset();
        
        Utils.hideError();
    }

    // 显示主应用页面
    showMainApp() {
        const loginPage = document.getElementById('loginPage');
        const mainApp = document.getElementById('mainApp');
        
        if (loginPage) loginPage.style.display = 'none';
        if (mainApp) mainApp.style.display = 'block';
    }

    // 更新用户信息显示
    updateUserInfo() {
        const userEmailEl = document.getElementById('userEmail');
        if (userEmailEl && this.currentUser) {
            userEmailEl.textContent = this.currentUser.email;
        }
    }

    // 获取当前用户
    getCurrentUser() {
        return this.currentUser;
    }

    // 检查用户是否已登录
    isAuthenticated() {
        return !!this.currentUser;
    }

    // 创建管理员账户（仅用于开发测试）
    async createAdminAccount(email, password) {
        try {
            const auth = window.firebaseService.getAuth();
            const userCredential = await auth.createUserWithEmailAndPassword(email, password);
            
            console.log('管理员账户创建成功:', userCredential.user.email);
            return userCredential.user;
        } catch (error) {
            console.error('创建管理员账户失败:', error);
            throw error;
        }
    }
}

// 页面导航管理
class NavigationManager {
    constructor() {
        this.currentPage = 'dashboard';
        this.init();
    }

    init() {
        this.setupNavigation();
    }

    // 设置导航事件
    setupNavigation() {
        const navItems = document.querySelectorAll('.nav-item');
        
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const page = item.getAttribute('data-page');
                this.navigateTo(page);
            });
        });
    }

    // 导航到指定页面
    navigateTo(page) {
        // 更新导航状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        
        document.querySelector(`[data-page="${page}"]`).classList.add('active');

        // 显示对应页面
        document.querySelectorAll('.content-page').forEach(pageEl => {
            pageEl.classList.remove('active');
        });
        
        const targetPage = document.getElementById(`${page}Page`);
        if (targetPage) {
            targetPage.classList.add('active');
        }

        this.currentPage = page;

        // 触发页面切换事件
        window.dispatchEvent(new CustomEvent('pageChanged', { 
            detail: { page } 
        }));
    }

    // 获取当前页面
    getCurrentPage() {
        return this.currentPage;
    }
}

// 创建全局实例
window.authManager = new AuthManager();
window.navigationManager = new NavigationManager();
