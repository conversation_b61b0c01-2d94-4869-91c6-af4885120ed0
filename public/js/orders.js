// 订单管理类
class OrderManager extends DatabaseService {
    constructor() {
        super('orders');
        this.studentsCache = new Map();
        this.packagesCache = new Map();
        this.init();
    }

    init() {
        window.addEventListener('firebaseReady', () => {
            this.setupEventListeners();
            this.loadOrders();
        });
    }

    // 设置事件监听器
    setupEventListeners() {
        // 添加订单按钮
        const addOrderBtn = document.getElementById('addOrderBtn');
        if (addOrderBtn) {
            addOrderBtn.addEventListener('click', () => {
                this.showAddOrderModal();
            });
        }

        // 监听页面切换事件
        window.addEventListener('pageChanged', (e) => {
            if (e.detail.page === 'orders') {
                this.loadOrders();
            }
        });
    }

    // 加载订单列表
    async loadOrders() {
        try {
            const orders = await this.getAll('createdAt', 'desc');
            await Promise.all([
                this.loadStudentsCache(),
                this.loadPackagesCache()
            ]);
            this.renderOrdersTable(orders);
        } catch (error) {
            console.error('加载订单列表失败:', error);
            alert('加载订单列表失败，请刷新页面重试');
        }
    }

    // 加载学生缓存
    async loadStudentsCache() {
        try {
            const studentsService = new DatabaseService('students');
            const students = await studentsService.getAll();

            this.studentsCache.clear();
            students.forEach(student => {
                this.studentsCache.set(student.id, student);
            });
        } catch (error) {
            console.error('加载学生数据失败:', error);
        }
    }

    // 加载课包缓存
    async loadPackagesCache() {
        try {
            const packagesService = new DatabaseService('packages');
            const packages = await packagesService.getAll();

            this.packagesCache.clear();
            packages.forEach(pkg => {
                this.packagesCache.set(pkg.id, pkg);
            });
        } catch (error) {
            console.error('加载课包数据失败:', error);
        }
    }

    // 渲染订单表格
    renderOrdersTable(orders) {
        const tbody = document.querySelector('#ordersTable tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (orders.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" style="text-align: center; padding: 40px; color: #666;">
                        暂无订单数据
                    </td>
                </tr>
            `;
            return;
        }

        orders.forEach(order => {
            const student = this.studentsCache.get(order.studentId);
            const studentName = student ? student.name : '未知学生';
            
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${order.orderNumber || ''}</td>
                <td>${studentName}</td>
                <td>${this.getOrderTypeText(order.orderType)}</td>
                <td>${Utils.formatCurrency(order.totalAmount)}</td>
                <td>
                    <span class="status-badge status-${order.status || 'pending'}">
                        ${this.getStatusText(order.status)}
                    </span>
                </td>
                <td>${window.firebaseService.formatTimestamp(order.createdAt)}</td>
                <td>
                    ${order.status === 'pending' ? `
                        <button class="btn btn-sm btn-success" onclick="orderManager.markAsPaid('${order.id}')">
                            <i class="fas fa-check"></i> 标记已付
                        </button>
                    ` : ''}
                    <button class="btn btn-sm btn-outline" onclick="orderManager.viewOrderDetails('${order.id}')">
                        <i class="fas fa-eye"></i> 详情
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="orderManager.deleteOrder('${order.id}', '${order.orderNumber}')">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // 获取订单类型文本
    getOrderTypeText(orderType) {
        const typeMap = {
            'course_package': '课时包',
            'material': '教材',
            'other': '其他'
        };
        return typeMap[orderType] || '未知';
    }

    // 获取状态文本
    getStatusText(status) {
        const statusMap = {
            'pending': '待付款',
            'paid': '已付款',
            'cancelled': '已取消',
            'refunded': '已退款'
        };
        return statusMap[status] || '待付款';
    }

    // 显示添加订单模态框
    async showAddOrderModal() {
        await Promise.all([
            this.loadStudentsCache(),
            this.loadPackagesCache()
        ]);
        this.showOrderModal();
    }

    // 标记订单为已付款
    async markAsPaid(orderId) {
        if (!confirm('确定要标记此订单为已付款吗？')) {
            return;
        }

        try {
            await this.update(orderId, {
                status: 'paid',
                paidAt: window.firebaseService.timestamp()
            });
            
            alert('订单状态更新成功');
            this.loadOrders();
        } catch (error) {
            console.error('更新订单状态失败:', error);
            alert('更新订单状态失败，请重试');
        }
    }

    // 查看订单详情
    async viewOrderDetails(orderId) {
        try {
            const order = await this.get(orderId);
            if (order) {
                this.showOrderDetailsModal(order);
            }
        } catch (error) {
            console.error('获取订单详情失败:', error);
            alert('获取订单详情失败');
        }
    }

    // 删除订单
    async deleteOrder(orderId, orderNumber) {
        if (!confirm(`确定要删除订单 "${orderNumber}" 吗？此操作不可恢复。`)) {
            return;
        }

        try {
            await this.delete(orderId);
            alert('订单删除成功');
            this.loadOrders();
        } catch (error) {
            console.error('删除订单失败:', error);
            alert('删除订单失败，请重试');
        }
    }

    // 显示订单模态框
    showOrderModal(order = null) {
        const isEdit = !!order;
        const modalTitle = isEdit ? '编辑订单' : '创建订单';
        
        // 生成学生选项
        let studentOptions = '<option value="">请选择学生</option>';
        this.studentsCache.forEach((student, id) => {
            const selected = order?.studentId === id ? 'selected' : '';
            studentOptions += `<option value="${id}" ${selected}>${student.name}</option>`;
        });

        // 生成课包选项
        let packageOptions = '<option value="">请选择课包</option>';
        this.packagesCache.forEach((pkg, id) => {
            const selected = order?.packageId === id ? 'selected' : '';
            packageOptions += `<option value="${id}" ${selected}>${pkg.name} (${pkg.hours}课时 - ¥${pkg.price})</option>`;
        });
        
        const modalHtml = `
            <div class="modal-overlay" id="orderModal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>${modalTitle}</h3>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <form id="orderForm" class="modal-body">
                        <div class="form-group">
                            <label for="orderStudentId">学生 *</label>
                            <select id="orderStudentId" required>
                                ${studentOptions}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="orderType">订单类型 *</label>
                            <select id="orderType" required onchange="orderManager.onOrderTypeChange(this.value)">
                                <option value="">请选择订单类型</option>
                                <option value="course_package" ${order?.orderType === 'course_package' ? 'selected' : ''}>课时包</option>
                                <option value="material" ${order?.orderType === 'material' ? 'selected' : ''}>教材</option>
                                <option value="other" ${order?.orderType === 'other' ? 'selected' : ''}>其他</option>
                            </select>
                        </div>
                        <div class="form-group" id="packageSelectGroup" style="display: none;">
                            <label for="orderPackageId">选择课包 *</label>
                            <select id="orderPackageId" onchange="orderManager.onPackageChange(this.value)">
                                ${packageOptions}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="orderDescription">订单描述 *</label>
                            <input type="text" id="orderDescription" required value="${order?.description || ''}" placeholder="例如：数学一对一课时包 20课时">
                        </div>
                        <div class="form-group">
                            <label for="orderAmount">订单金额（元）*</label>
                            <input type="number" id="orderAmount" required min="0" step="0.01" value="${order?.totalAmount || ''}" placeholder="例如：2000">
                        </div>
                        <div class="form-group">
                            <label for="paymentMethod">支付方式</label>
                            <select id="paymentMethod">
                                <option value="cash" ${order?.paymentMethod === 'cash' ? 'selected' : ''}>现金</option>
                                <option value="wechat" ${order?.paymentMethod === 'wechat' ? 'selected' : ''}>微信</option>
                                <option value="alipay" ${order?.paymentMethod === 'alipay' ? 'selected' : ''}>支付宝</option>
                                <option value="bank_transfer" ${order?.paymentMethod === 'bank_transfer' ? 'selected' : ''}>银行转账</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="orderNotes">备注</label>
                            <textarea id="orderNotes" rows="3" placeholder="订单备注信息">${order?.notes || ''}</textarea>
                        </div>
                    </form>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">
                            取消
                        </button>
                        <button type="submit" class="btn btn-primary" onclick="orderManager.saveOrder('${order?.id || ''}')">
                            ${isEdit ? '更新' : '创建'}
                        </button>
                    </div>
                </div>
            </div>
        `;

        const modalContainer = document.getElementById('modalContainer');
        modalContainer.innerHTML = modalHtml;
    }

    // 显示订单详情模态框
    showOrderDetailsModal(order) {
        const student = this.studentsCache.get(order.studentId);
        const studentName = student ? student.name : '未知学生';
        
        const modalHtml = `
            <div class="modal-overlay" id="orderDetailsModal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>订单详情</h3>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="order-details">
                            <div class="detail-row">
                                <label>订单号：</label>
                                <span>${order.orderNumber || ''}</span>
                            </div>
                            <div class="detail-row">
                                <label>学生：</label>
                                <span>${studentName}</span>
                            </div>
                            <div class="detail-row">
                                <label>订单类型：</label>
                                <span>${this.getOrderTypeText(order.orderType)}</span>
                            </div>
                            <div class="detail-row">
                                <label>订单描述：</label>
                                <span>${order.description || ''}</span>
                            </div>
                            <div class="detail-row">
                                <label>订单金额：</label>
                                <span>${Utils.formatCurrency(order.totalAmount)}</span>
                            </div>
                            <div class="detail-row">
                                <label>支付方式：</label>
                                <span>${order.paymentMethod || ''}</span>
                            </div>
                            <div class="detail-row">
                                <label>订单状态：</label>
                                <span class="status-badge status-${order.status || 'pending'}">
                                    ${this.getStatusText(order.status)}
                                </span>
                            </div>
                            <div class="detail-row">
                                <label>创建时间：</label>
                                <span>${window.firebaseService.formatTimestamp(order.createdAt)}</span>
                            </div>
                            ${order.paidAt ? `
                                <div class="detail-row">
                                    <label>付款时间：</label>
                                    <span>${window.firebaseService.formatTimestamp(order.paidAt)}</span>
                                </div>
                            ` : ''}
                            ${order.notes ? `
                                <div class="detail-row">
                                    <label>备注：</label>
                                    <span>${order.notes}</span>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">
                            关闭
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 添加详情页面样式
        if (!document.getElementById('orderDetailsStyles')) {
            const detailsStyles = document.createElement('style');
            detailsStyles.id = 'orderDetailsStyles';
            detailsStyles.textContent = `
                .order-details {
                    padding: 10px 0;
                }
                .detail-row {
                    display: flex;
                    margin-bottom: 15px;
                    align-items: center;
                }
                .detail-row label {
                    font-weight: 600;
                    min-width: 100px;
                    color: #555;
                }
                .detail-row span {
                    flex: 1;
                    color: #333;
                }
            `;
            document.head.appendChild(detailsStyles);
        }

        const modalContainer = document.getElementById('modalContainer');
        modalContainer.innerHTML = modalHtml;
    }

    // 保存订单信息
    async saveOrder(orderId) {
        const orderData = {
            studentId: document.getElementById('orderStudentId').value,
            orderType: document.getElementById('orderType').value,
            description: document.getElementById('orderDescription').value.trim(),
            totalAmount: parseFloat(document.getElementById('orderAmount').value),
            paymentMethod: document.getElementById('paymentMethod').value,
            notes: document.getElementById('orderNotes').value.trim()
        };

        // 如果是课时包订单，保存课包ID
        if (orderData.orderType === 'course_package') {
            const packageId = document.getElementById('orderPackageId').value;
            if (!packageId) {
                alert('请选择课包');
                return;
            }
            orderData.packageId = packageId;
        }

        // 验证必填字段
        if (!orderData.studentId) {
            alert('请选择学生');
            return;
        }
        if (!orderData.orderType) {
            alert('请选择订单类型');
            return;
        }
        if (!orderData.description) {
            alert('请输入订单描述');
            return;
        }
        if (!orderData.totalAmount || orderData.totalAmount <= 0) {
            alert('请输入有效的订单金额');
            return;
        }

        try {
            if (orderId) {
                // 更新订单
                await this.update(orderId, orderData);
                alert('订单信息更新成功');
            } else {
                // 创建订单
                orderData.orderNumber = Utils.generateOrderNumber();
                orderData.status = 'pending';
                
                await this.add(orderData);
                alert('订单创建成功');
            }

            // 关闭模态框
            document.getElementById('orderModal').remove();
            
            // 重新加载订单列表
            this.loadOrders();
        } catch (error) {
            console.error('保存订单信息失败:', error);
            alert('保存失败，请重试');
        }
    }

    // 订单类型变化处理
    onOrderTypeChange(orderType) {
        const packageSelectGroup = document.getElementById('packageSelectGroup');
        const orderAmountInput = document.getElementById('orderAmount');
        const orderDescriptionInput = document.getElementById('orderDescription');

        if (orderType === 'course_package') {
            // 显示课包选择框
            packageSelectGroup.style.display = 'block';
            // 清空金额和描述，等待选择课包后自动填入
            orderAmountInput.value = '';
            orderDescriptionInput.value = '';
        } else {
            // 隐藏课包选择框
            packageSelectGroup.style.display = 'none';
            // 清空课包选择
            const packageSelect = document.getElementById('orderPackageId');
            if (packageSelect) packageSelect.value = '';
        }
    }

    // 课包选择变化处理
    onPackageChange(packageId) {
        if (!packageId) return;

        const pkg = this.packagesCache.get(packageId);
        if (!pkg) return;

        // 自动填入课包价格和描述
        const orderAmountInput = document.getElementById('orderAmount');
        const orderDescriptionInput = document.getElementById('orderDescription');

        if (orderAmountInput) {
            orderAmountInput.value = pkg.price;
        }

        if (orderDescriptionInput) {
            orderDescriptionInput.value = `${pkg.name} (${pkg.hours}课时)`;
        }
    }

    // 标记订单为已付款（修改版，支持课时记录生成）
    async markAsPaid(orderId) {
        if (!confirm('确定要标记此订单为已付款吗？订单完成后将自动为学生增加对应课时。')) {
            return;
        }

        try {
            const order = await this.get(orderId);
            if (!order) {
                alert('订单不存在');
                return;
            }

            // 更新订单状态
            await this.update(orderId, {
                status: 'paid',
                paidAt: window.firebaseService.timestamp()
            });

            // 如果是课时包订单，生成课时记录
            if (order.orderType === 'course_package' && order.packageId) {
                await this.generateCourseRecord(order);
            }

            alert('订单已标记为已付款');
            this.loadOrders();
        } catch (error) {
            console.error('标记订单失败:', error);
            alert('标记订单失败，请重试');
        }
    }

    // 生成课时记录
    async generateCourseRecord(order) {
        try {
            const pkg = this.packagesCache.get(order.packageId);
            if (!pkg) {
                console.error('课包信息不存在:', order.packageId);
                return;
            }

            // 创建课时记录
            const courseRecordsService = new DatabaseService('courseRecords');
            const courseRecord = {
                studentId: order.studentId,
                packageId: order.packageId,
                packageName: pkg.name,
                orderId: order.id,
                orderNumber: order.orderNumber,
                type: 'recharge',
                hours: pkg.hours,
                amount: pkg.price,
                notes: `订单充值：${order.orderNumber}`,
                createdAt: window.firebaseService.timestamp()
            };

            await courseRecordsService.add(courseRecord);
            console.log('课时记录生成成功:', courseRecord);
        } catch (error) {
            console.error('生成课时记录失败:', error);
            // 不阻断订单完成流程，只记录错误
        }
    }
}

// 创建全局实例
window.orderManager = new OrderManager();
