# 培训班管理系统

一个基于 Firebase 的现代化培训班管理系统，提供学生管理、课时管理和订单管理等核心功能。

## 功能特性

### 🎓 学生管理
- 学生信息的增删改查
- 学生状态管理（在读、休学、毕业）
- 学生档案详细记录
- 支持按年级分类管理

### ⏰ 课时管理
- 课时包创建和管理
- 课时消耗记录和跟踪
- 剩余课时实时显示
- 课时使用历史查询

### 🛒 订单管理
- 订单创建和状态跟踪
- 支持多种支付方式
- 订单详情查看
- 收入统计和报表

### 📊 数据统计
- 实时仪表盘显示
- 学生数量统计
- 课时使用情况
- 收入统计分析

## 技术架构

### 前端技术
- **HTML5/CSS3/JavaScript**: 原生前端技术栈
- **Font Awesome**: 图标库
- **响应式设计**: 支持移动端和桌面端

### 后端服务
- **Firebase Authentication**: 用户认证
- **Cloud Firestore**: 数据库存储
- **Firebase Hosting**: 静态网站托管
- **Firebase Security Rules**: 数据安全控制

### 数据结构
```javascript
// 学生集合
students: {
  studentId: {
    name: "学生姓名",
    phone: "联系电话",
    email: "邮箱地址",
    grade: "年级",
    status: "状态", // active, inactive, graduated
    notes: "备注信息",
    createdAt: "创建时间",
    updatedAt: "更新时间"
  }
}

// 课时包集合
coursePackages: {
  packageId: {
    studentId: "学生ID",
    packageName: "课时包名称",
    totalHours: "总课时数",
    usedHours: "已用课时",
    remainingHours: "剩余课时",
    pricePerHour: "单价",
    totalAmount: "总金额",
    purchaseDate: "购买日期",
    notes: "备注"
  }
}

// 订单集合
orders: {
  orderId: {
    orderNumber: "订单号",
    studentId: "学生ID",
    orderType: "订单类型", // course_package, material, other
    description: "订单描述",
    totalAmount: "订单金额",
    status: "订单状态", // pending, paid, cancelled, refunded
    paymentMethod: "支付方式",
    createdAt: "创建时间",
    paidAt: "付款时间"
  }
}
```

## 快速开始

### 环境要求
- Node.js 18+
- Firebase CLI
- 现代浏览器

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd verywill-firebase
```

2. **安装依赖**
```bash
npm install
```

3. **配置 Firebase**
```bash
# 登录 Firebase
firebase login

# 初始化项目
firebase init

# 选择以下服务：
# - Hosting
# - Firestore
# - Authentication
```

4. **配置 Firestore 安全规则**
```bash
# 部署安全规则
firebase deploy --only firestore:rules
```

5. **启用 Authentication**
- 在 Firebase Console 中启用 Email/Password 认证
- 创建管理员账户

6. **本地开发**
```bash
# 启动本地服务器
firebase serve --only hosting

# 或使用 Python 简单服务器
python3 -m http.server 8080 --directory public
```

7. **部署到生产环境**
```bash
firebase deploy
```

### 开发环境设置

在开发环境中，系统提供了便捷的测试功能：

1. **创建管理员账户**
```javascript
// 在浏览器控制台中执行
createAdminAccount('<EMAIL>', 'admin123');
```

2. **创建测试数据**
```javascript
// 在浏览器控制台中执行
createTestData();
```

## 使用指南

### 登录系统
1. 访问系统网址
2. 使用管理员邮箱和密码登录
3. 系统会自动跳转到仪表盘

### 学生管理
1. 点击"学生管理"菜单
2. 点击"添加学生"按钮创建新学生
3. 填写学生基本信息并保存
4. 可以编辑或删除现有学生信息

### 课时管理
1. 点击"课时管理"菜单
2. 点击"添加课时包"创建课时包
3. 选择学生并填写课时包信息
4. 使用"消课"功能记录课时消耗

### 订单管理
1. 点击"订单管理"菜单
2. 点击"创建订单"新建订单
3. 填写订单详情和金额
4. 使用"标记已付"更新订单状态

## 项目结构

```
verywill-firebase/
├── public/                 # 前端文件
│   ├── css/
│   │   └── styles.css     # 样式文件
│   ├── js/
│   │   ├── app.js         # 主应用文件
│   │   ├── auth.js        # 认证模块
│   │   ├── students.js    # 学生管理
│   │   ├── courses.js     # 课时管理
│   │   ├── orders.js      # 订单管理
│   │   ├── dashboard.js   # 仪表盘
│   │   └── firebase-config.js # Firebase配置
│   └── index.html         # 主页面
├── firebase.json          # Firebase配置
├── firestore.rules        # 数据库安全规则
├── package.json           # 项目依赖
└── README.md             # 项目说明
```

## 安全考虑

### 数据安全
- 所有数据操作都需要用户认证
- Firestore 安全规则限制数据访问
- 敏感信息加密存储

### 用户认证
- 使用 Firebase Authentication
- 支持邮箱密码认证
- 会话管理和自动登出

## 扩展功能

系统设计具有良好的扩展性，可以轻松添加以下功能：

- 📱 移动端应用
- 📧 邮件通知
- 📅 课程安排
- 💰 在线支付
- 📈 高级报表
- 👥 多角色权限
- 🔔 消息推送

## 技术支持

如果您在使用过程中遇到问题，请：

1. 检查浏览器控制台错误信息
2. 确认 Firebase 配置正确
3. 验证网络连接状态
4. 查看 Firebase Console 中的错误日志

## 许可证

本项目采用 MIT 许可证。详情请参阅 LICENSE 文件。

---

**培训班管理系统** - 让教育管理更简单高效！
